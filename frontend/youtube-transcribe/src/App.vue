<script setup>
import { ref, onMounted } from 'vue';
import VideoInput from './components/VideoInput.vue';
import VideoPreview from './components/VideoPreview.vue';
import ExampleVideos from './components/ExampleVideos.vue';
import SocialProof from './components/SocialProof.vue';
import Navbar from './components/Navbar.vue';
import FinalTabsContainer from './components/FinalTabsContainer.vue';
import { checkApiConnectivity } from './debug.js';

const selectedVideoId = ref('');
const videoPreviewRef = ref(null);
const currentVideoTime = ref(0);
const examplesCollapsed = ref(false);
const userVideos = ref([]);
const userVideoDetails = ref({});

function handleVideoSelected(videoId) {
  selectedVideoId.value = videoId;

  // Scroll to video content when a video is selected
  setTimeout(() => {
    const videoContent = document.querySelector('.video-content');
    if (videoContent) {
      videoContent.scrollIntoView({ behavior: 'smooth' });
    }
  }, 100);
}

function handlePlayVideo(videoId) {
  selectedVideoId.value = videoId;

  // Scroll to video content when playing from the gallery
  setTimeout(() => {
    const videoContent = document.querySelector('.video-content');
    if (videoContent) {
      videoContent.scrollIntoView({ behavior: 'smooth' });
    }
  }, 100);
}

function handleAddVideo(videoId) {
  console.log("Adding video:", videoId);

  // Add the video to userVideos array if it's not already there and it's valid
  if (videoId && !userVideos.value.includes(videoId)) {
    // Add basic details immediately
    userVideoDetails.value[videoId] = {
      title: 'Video ' + videoId,
      thumbnail: `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`
    };

    // Add to user videos array at the end (so new videos appear on the right)
    userVideos.value.push(videoId);

    // Set as selected video
    selectedVideoId.value = videoId;

    // Try to fetch more details in the background
    fetchVideoDetails(videoId);
  }

  // Focus on the input field and clear it for a new video
  const inputField = document.querySelector('input[placeholder="Paste YouTube URL here"]');
  if (inputField) {
    inputField.focus();
    inputField.value = '';
    // Scroll to the input field
    inputField.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

// Separate function to fetch video details
async function fetchVideoDetails(videoId) {
  // First try the oEmbed API directly as it's more reliable for titles
  try {
    const ytResponse = await fetch(`https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`);
    const ytData = await ytResponse.json();

    if (ytData && ytData.title) {
      userVideoDetails.value[videoId] = {
        title: ytData.title,
        thumbnail: `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`
      };
      console.log('Updated video details from oEmbed (primary):', videoId, ytData.title);
      return; // Exit early if we got the title
    }
  } catch (ytError) {
    console.error('Error fetching video details from YouTube oEmbed (primary):', ytError);
    // Continue to try the backend API
  }

  // If oEmbed fails, try our backend API
  try {
    // Fetch video details from YouTube API via our backend
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    const response = await fetch(`${API_URL}/video/${videoId}`);
    const data = await response.json();

    // The API returns the full YouTube API response
    if (data && data.snippet && data.snippet.title) {
      // Update video details with the actual title from YouTube
      userVideoDetails.value[videoId] = {
        title: data.snippet.title,
        thumbnail: `https://img.youtube.com/vi/${videoId}/mqdefault.jpg`
      };
      console.log('Updated video details from backend API:', videoId, data.snippet.title);
    }
  } catch (error) {
    console.error('Error fetching video details from backend API:', error);
  }
}

function handleTimeUpdate(time) {
  currentVideoTime.value = time;
}

function handleSeekToTime(time, forcePlay = false) {
  console.log('App: handleSeekToTime called with time:', time, 'forcePlay:', forcePlay, 'for video:', selectedVideoId.value);

  if (videoPreviewRef.value) {
    // Ensure the video player is ready before seeking
    setTimeout(() => {
      // Pass the forcePlay flag to ensure autoplay when clicking on timestamps
      videoPreviewRef.value.seekToTime(time, forcePlay);
    }, 100);
  } else {
    console.warn('App: videoPreviewRef is not available');
  }
}

// Handle subtitle loading state changes
function handleSubtitlesLoadingState(isLoading) {
  console.log('App: Subtitles loading state changed to:', isLoading);

  if (videoPreviewRef.value) {
    // Update the video player's subtitle loading state
    videoPreviewRef.value.setSubtitleLoadingState(isLoading);
  } else {
    console.warn('App: videoPreviewRef is not available for subtitle loading state update');
  }
}

// Function to download transcript
async function downloadTranscript() {
  if (!selectedVideoId.value) return;

  try {
    // Get the download URL
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    const downloadUrl = `${API_URL}/captions/${selectedVideoId.value}/download?format=txt`;

    // Create a temporary link and trigger the download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.setAttribute('download', `youtube-transcript-${selectedVideoId.value}.txt`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (err) {
    console.error('Error downloading transcript:', err);
    alert('Failed to download transcript');
  }
}

// Function to copy transcript to clipboard
async function copyTranscript() {
  if (!selectedVideoId.value) return;

  try {
    // Fetch the transcript
    const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
    const response = await fetch(`${API_URL}/transcript/${selectedVideoId.value}`);
    const data = await response.json();

    if (data && data.transcript) {
      // Extract text from transcript
      const text = data.transcript.map(item => item.text).join('\n');

      // Copy to clipboard
      await navigator.clipboard.writeText(text);

      // Show success message
      alert('Transcript copied to clipboard!');
    }
  } catch (err) {
    console.error('Error copying transcript:', err);
    alert('Failed to copy transcript');
  }
}

function toggleExamples() {
  examplesCollapsed.value = !examplesCollapsed.value;
}

// Check API connectivity when the app mounts
onMounted(async () => {
  console.log('App mounted, checking API connectivity...');
  const isConnected = await checkApiConnectivity();
  console.log('API connectivity check result:', isConnected);
});
</script>

<template>
  <div class="min-h-screen bg-base-200 flex flex-col">
    <!-- Navigation Bar -->
    <Navbar />

    <div class="w-[85%] mx-auto px-4 py-8 flex-grow">
      <main class="space-y-8">
        <!-- Hero Section with Input and Examples -->
        <div class="py-6">
          <VideoInput
            @video-selected="handleVideoSelected"
            @play-video="handlePlayVideo"
            @add-video="handleAddVideo"
            :examplesCollapsed="examplesCollapsed"
            :userVideos="userVideos"
            :userVideoDetails="userVideoDetails"
            :selectedVideoId="selectedVideoId"
            @toggle-examples="toggleExamples"
          />
        </div>

        <!-- Video Content - Two Column Layout with Separate Cards -->
        <div v-if="selectedVideoId" class="flex flex-col md:flex-row gap-6 video-content">
          <!-- Left Card - Video and Transcript Actions -->
          <div class="md:w-2/5">
            <div class="card bg-base-100 shadow-md h-full">
              <div class="card-body">
                <VideoPreview
                  ref="videoPreviewRef"
                  :videoId="selectedVideoId"
                  @time-update="handleTimeUpdate"
                />

                <!-- Download/Copy Transcript CTA -->
                <div class="w-full mt-4">
                  <div class="flex items-center mb-2">
                    <span class="font-medium mr-2">Get the transcript:</span>
                    <div class="flex gap-2 flex-1">
                      <button class="btn btn-outline flex-1" @click="copyTranscript">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                        </svg>
                        Copy
                      </button>
                      <button class="btn btn-primary flex-1" @click="downloadTranscript">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        Download
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Card - Transcript and Tools -->
          <div class="md:w-3/5">
            <div class="card bg-base-100 shadow-md h-full">
              <div class="card-body p-4">
                <FinalTabsContainer
                  :videoId="selectedVideoId"
                  @seek-to-time="handleSeekToTime"
                  @subtitles-loading-state="handleSubtitlesLoadingState"
                />
              </div>
            </div>
          </div>
        </div>



        <!-- Social Proof Section removed as requested -->
      </main>

      <!-- Footer -->
      <footer class="footer footer-center p-4 text-base-content/70 mt-12 border-t border-base-300">
        <p>&copy; {{ new Date().getFullYear() }} YouTube Subtitle Viewer</p>
      </footer>
    </div>
  </div>
</template>
