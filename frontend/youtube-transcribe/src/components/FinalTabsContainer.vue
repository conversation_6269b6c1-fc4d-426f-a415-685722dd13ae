<template>
  <div class="tabs-container">
    <!-- Tab Navigation -->
    <div class="tab-buttons mb-4 border-b pb-2">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        class="tab-button px-4 py-2 rounded-md font-medium mr-2"
        :class="{ 'active-tab': activeTab === tab.id }"
        @click="setActiveTab(tab.id)"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content-container">
      <!-- Transcript Tab -->
      <template v-if="activeTab === 'transcript'">
        <div class="transcript-container">
          <div class="flex justify-between items-center mb-4">
            <div>
              <h2 class="text-xl font-bold">Video Transcript</h2>
              <p class="text-sm text-base-content/70 mt-1">Click on any timestamp to jump to that point in the video.</p>
            </div>

            <!-- Language Dropdown -->
            <div v-if="availableLanguages.length > 0" class="language-dropdown">
              <div class="dropdown dropdown-end">
                <label tabindex="0" class="btn btn-sm m-1 gap-2">
                  <span v-if="selectedLanguage.flag" class="flag-icon">{{ selectedLanguage.flag }}</span>
                  <span>{{ selectedLanguage.name || 'Select Language' }}</span>
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </label>
                <div tabindex="0" class="dropdown-content z-[1] shadow bg-base-100 rounded-box w-80 language-dropdown">
                  <!-- Search input -->
                  <div class="border-b border-base-300">
                    <div class="relative px-4 py-2">
                      <input
                        type="text"
                        v-model="languageSearch"
                        placeholder="Search languages..."
                        class="input input-sm input-bordered w-full pr-8"
                        @click.stop
                      />
                      <button
                        v-if="languageSearch"
                        @click="languageSearch = ''"
                        class="absolute right-6 top-1/2 -translate-y-1/2 text-base-content/50 hover:text-base-content"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>

                  <!-- Language list -->
                  <div class="max-h-60 overflow-y-auto py-2 w-full">
                    <ul class="menu menu-compact w-full">
                      <li v-for="lang in filteredLanguages" :key="lang.code"
                          @click="changeLanguage(lang.code)"
                          class="hover:bg-base-200 cursor-pointer w-full"
                          :class="{
                            'bg-primary/10': selectedLanguage.code === lang.code
                          }">
                        <a class="flex items-center gap-2 px-4 py-2 w-full">
                          <span class="flag-icon w-6 text-center">{{ lang.flag || '🌐' }}</span>
                          <span class="flex-grow">{{ lang.name }}</span>
                          <span v-if="lang.isAvailable && lang.trackKind === 'standard'"
                                class="badge badge-xs badge-primary whitespace-nowrap">Manual</span>
                          <span v-else-if="lang.trackKind === 'auto-generated' || lang.isAutoGenerated"
                                class="badge badge-xs badge-secondary whitespace-nowrap">Auto generated</span>
                        </a>
                      </li>
                      <li v-if="filteredLanguages.length === 0" class="px-4 py-2 text-base-content/50 text-center w-full">
                        No languages found
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="loading" class="flex flex-col items-center justify-center py-8 space-y-4">
            <div class="relative">
              <div class="w-16 h-16 flex items-center justify-center">
                <svg class="animate-spin h-10 w-10 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
            </div>
            <p class="text-base-content/70 font-medium">Loading transcript...</p>
          </div>

          <div v-else-if="error" class="alert alert-error mb-4">
            <span>{{ error }}</span>
          </div>

          <div v-else-if="!transcript || transcript.length === 0" class="alert alert-warning mb-4">
            <span>No transcript data available for this video.</span>
          </div>

          <div v-else class="transcript-list bg-base-100 p-4 rounded-lg border border-base-300">
            <table class="w-full">
              <thead>
                <tr>
                  <th class="text-left pb-2 w-24">Timestamp</th>
                  <th class="text-left pb-2">Text</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in transcript" :key="index" class="border-b border-base-200 hover:bg-base-200">
                  <td class="py-2 pr-4 font-medium text-primary whitespace-nowrap">
                    <button
                      class="px-2 py-1 rounded hover:bg-primary hover:text-white transition-colors"
                      @click="seekToTime(item.start)"
                    >
                      {{ item.formattedStart || formatTime(item.start) }}
                    </button>
                  </td>
                  <td class="py-2">{{ item.text }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </template>

      <!-- Summary Tab -->
      <template v-else-if="activeTab === 'summary'">
        <div class="coming-soon-container">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Video Summary</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will provide an AI-generated summary of the video content, highlighting the key points and main ideas.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </template>

      <!-- Quotes Tab -->
      <template v-else-if="activeTab === 'quotes'">
        <div class="coming-soon-container">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Key Quotes</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will extract important quotes and memorable statements from the video, making them easy to reference and share.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </template>

      <!-- Descriptions Tab -->
      <template v-else-if="activeTab === 'descriptions'">
        <div class="coming-soon-container">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Video Descriptions</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will generate alternative descriptions for your video, optimized for different platforms and audiences.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </template>

      <!-- Social Media Tab -->
      <template v-else-if="activeTab === 'social'">
        <div class="coming-soon-container">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Social Media Content</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will generate social media posts based on your video content, tailored for different platforms like Twitter, LinkedIn, and Instagram.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </template>

      <!-- Magic Chat Tab -->
      <template v-else-if="activeTab === 'chat'">
        <div class="coming-soon-container">
          <div class="w-16 h-16 mb-4 text-primary">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-2">Magic Chat</h3>
          <p class="text-base-content/70 mb-6 max-w-md">
            This feature will allow you to ask questions about the video content and get AI-powered answers, making it easier to understand and explore the material.
          </p>
          <div class="badge badge-primary badge-outline">Coming Soon</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    videoId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      activeTab: 'transcript',
      tabs: [
        { id: 'transcript', label: 'Transcript' },
        { id: 'summary', label: 'Summary' },
        { id: 'quotes', label: 'Quotes' },
        { id: 'descriptions', label: 'Descriptions' },
        { id: 'social', label: 'Social Media' },
        { id: 'chat', label: 'Magic Chat' }
      ],
      loading: false,
      error: '',
      transcript: [],
      availableLanguages: [],
      selectedLanguage: { code: 'en', name: 'English', flag: '🇺🇸' },
      languageSearch: '', // For searching languages
      currentRequest: null, // Track current request to prevent duplicates
      requestDebounceTimer: null, // Timer for debouncing requests
      languageMap: {
        // Main languages with ISO 639-1 codes
        'en': { name: 'English', flag: '🇺🇸' },
        'es': { name: 'Spanish', flag: '🇪🇸' },
        'fr': { name: 'French', flag: '🇫🇷' },
        'de': { name: 'German', flag: '🇩🇪' },
        'it': { name: 'Italian', flag: '🇮🇹' },
        'pt': { name: 'Portuguese', flag: '🇵🇹' },
        'ru': { name: 'Russian', flag: '🇷🇺' },
        'ja': { name: 'Japanese', flag: '🇯🇵' },
        'ko': { name: 'Korean', flag: '🇰🇷' },
        'zh': { name: 'Chinese', flag: '🇨🇳' },
        'zh-Hans': { name: 'Chinese (Simplified)', flag: '🇨🇳' },
        'zh-Hant': { name: 'Chinese (Traditional)', flag: '🇹🇼' },
        'ar': { name: 'Arabic', flag: '🇸🇦' },
        'hi': { name: 'Hindi', flag: '🇮🇳' },
        'nl': { name: 'Dutch', flag: '🇳🇱' },
        'sv': { name: 'Swedish', flag: '🇸🇪' },
        'fi': { name: 'Finnish', flag: '🇫🇮' },
        'no': { name: 'Norwegian', flag: '🇳🇴' },
        'da': { name: 'Danish', flag: '🇩🇰' },
        'pl': { name: 'Polish', flag: '🇵🇱' },
        'tr': { name: 'Turkish', flag: '🇹🇷' },
        'cs': { name: 'Czech', flag: '🇨🇿' },
        'hu': { name: 'Hungarian', flag: '🇭🇺' },
        'el': { name: 'Greek', flag: '🇬🇷' },
        'th': { name: 'Thai', flag: '🇹🇭' },
        'vi': { name: 'Vietnamese', flag: '🇻🇳' },
        'id': { name: 'Indonesian', flag: '🇮🇩' },
        'ms': { name: 'Malay', flag: '🇲🇾' },
        'he': { name: 'Hebrew', flag: '🇮🇱' },
        'iw': { name: 'Modern Hebrew', flag: '🇮🇱' },
        'ro': { name: 'Romanian', flag: '🇷🇴' },
        'uk': { name: 'Ukrainian', flag: '🇺🇦' },
        'ur': { name: 'Urdu', flag: '🇵🇰' },

        // Regional variants - map to main languages
        'en-US': { name: 'English (US)', flag: '🇺🇸' },
        'en-GB': { name: 'English (UK)', flag: '🇬🇧' },
        'en-CA': { name: 'English (Canada)', flag: '🇨🇦' },
        'en-AU': { name: 'English (Australia)', flag: '🇦🇺' },
        'en-NZ': { name: 'English (New Zealand)', flag: '🇳🇿' },
        'en-IE': { name: 'English (Ireland)', flag: '🇮🇪' },
        'en-ZA': { name: 'English (South Africa)', flag: '🇿🇦' },
        'en-IN': { name: 'English (India)', flag: '🇮🇳' },
        'en-SG': { name: 'English (Singapore)', flag: '🇸🇬' },
        'en-PH': { name: 'English (Philippines)', flag: '🇵🇭' },

        'es-ES': { name: 'Spanish (Spain)', flag: '🇪🇸' },
        'es-MX': { name: 'Spanish (Mexico)', flag: '🇲🇽' },
        'es-AR': { name: 'Spanish (Argentina)', flag: '🇦🇷' },
        'es-CO': { name: 'Spanish (Colombia)', flag: '🇨🇴' },
        'es-CL': { name: 'Spanish (Chile)', flag: '🇨🇱' },
        'es-PE': { name: 'Spanish (Peru)', flag: '🇵🇪' },
        'es-VE': { name: 'Spanish (Venezuela)', flag: '🇻🇪' },
        'es-UY': { name: 'Spanish (Uruguay)', flag: '🇺🇾' },
        'es-PY': { name: 'Spanish (Paraguay)', flag: '🇵🇾' },
        'es-EC': { name: 'Spanish (Ecuador)', flag: '🇪🇨' },
        'es-GT': { name: 'Spanish (Guatemala)', flag: '🇬🇹' },
        'es-CU': { name: 'Spanish (Cuba)', flag: '🇨🇺' },
        'es-BO': { name: 'Spanish (Bolivia)', flag: '🇧🇴' },
        'es-DO': { name: 'Spanish (Dominican Republic)', flag: '🇩🇴' },
        'es-HN': { name: 'Spanish (Honduras)', flag: '🇭🇳' },
        'es-SV': { name: 'Spanish (El Salvador)', flag: '🇸🇻' },
        'es-NI': { name: 'Spanish (Nicaragua)', flag: '🇳🇮' },
        'es-CR': { name: 'Spanish (Costa Rica)', flag: '🇨🇷' },
        'es-PA': { name: 'Spanish (Panama)', flag: '🇵🇦' },
        'es-PR': { name: 'Spanish (Puerto Rico)', flag: '🇵🇷' },

        'fr-FR': { name: 'French (France)', flag: '🇫🇷' },
        'fr-CA': { name: 'French (Canada)', flag: '🇨🇦' },
        'fr-BE': { name: 'French (Belgium)', flag: '🇧🇪' },
        'fr-CH': { name: 'French (Switzerland)', flag: '🇨🇭' },
        'fr-LU': { name: 'French (Luxembourg)', flag: '🇱🇺' },
        'fr-MC': { name: 'French (Monaco)', flag: '🇲🇨' },

        'pt-PT': { name: 'Portuguese (Portugal)', flag: '🇵🇹' },
        'pt-BR': { name: 'Portuguese (Brazil)', flag: '🇧🇷' },
        'pt-AO': { name: 'Portuguese (Angola)', flag: '🇦🇴' },
        'pt-MZ': { name: 'Portuguese (Mozambique)', flag: '🇲🇿' },

        'de-DE': { name: 'German (Germany)', flag: '🇩🇪' },
        'de-AT': { name: 'German (Austria)', flag: '🇦🇹' },
        'de-CH': { name: 'German (Switzerland)', flag: '🇨🇭' },
        'de-LI': { name: 'German (Liechtenstein)', flag: '🇱🇮' },
        'de-LU': { name: 'German (Luxembourg)', flag: '🇱🇺' },

        'it-IT': { name: 'Italian (Italy)', flag: '🇮🇹' },
        'it-CH': { name: 'Italian (Switzerland)', flag: '🇨🇭' },
        'it-SM': { name: 'Italian (San Marino)', flag: '🇸🇲' },
        'it-VA': { name: 'Italian (Vatican City)', flag: '🇻🇦' },

        'ru-RU': { name: 'Russian (Russia)', flag: '🇷🇺' },
        'ru-BY': { name: 'Russian (Belarus)', flag: '🇧🇾' },
        'ru-KZ': { name: 'Russian (Kazakhstan)', flag: '🇰🇿' },
        'ru-KG': { name: 'Russian (Kyrgyzstan)', flag: '🇰🇬' },

        // Additional languages
        'af': { name: 'Afrikaans', flag: '🇿🇦' },
        'sq': { name: 'Albanian', flag: '🇦🇱' },
        'am': { name: 'Amharic', flag: '🇪🇹' },
        'hy': { name: 'Armenian', flag: '🇦🇲' },
        'az': { name: 'Azerbaijani', flag: '🇦🇿' },
        'eu': { name: 'Basque', flag: '🏴󠁥󠁳󠁰󠁶󠁿' },
        'be': { name: 'Belarusian', flag: '🇧🇾' },
        'bn': { name: 'Bengali', flag: '🇧🇩' },
        'bs': { name: 'Bosnian', flag: '🇧🇦' },
        'bg': { name: 'Bulgarian', flag: '🇧🇬' },
        'ca': { name: 'Catalan', flag: '🏴󠁥󠁳󠁣󠁴󠁿' },
        'ceb': { name: 'Cebuano', flag: '🇵🇭' },
        'ny': { name: 'Chichewa', flag: '🇲🇼' },
        'co': { name: 'Corsican', flag: '🇫🇷' },
        'hr': { name: 'Croatian', flag: '🇭🇷' },
        'et': { name: 'Estonian', flag: '🇪🇪' },
        'tl': { name: 'Filipino', flag: '🇵🇭' },
        'fil': { name: 'Filipino', flag: '🇵🇭' },
        'gl': { name: 'Galician', flag: '🏴󠁥󠁳󠁧󠁡󠁿' },
        'ka': { name: 'Georgian', flag: '🇬🇪' },
        'gu': { name: 'Gujarati', flag: '🇮🇳' },
        'ht': { name: 'Haitian Creole', flag: '🇭🇹' },
        'ha': { name: 'Hausa', flag: '🇳🇬' },
        'haw': { name: 'Hawaiian', flag: '🇺🇸' },
        'hmn': { name: 'Hmong', flag: '🇨🇳' },
        'is': { name: 'Icelandic', flag: '🇮🇸' },
        'ig': { name: 'Igbo', flag: '🇳🇬' },
        'ga': { name: 'Irish', flag: '🇮🇪' },
        'jw': { name: 'Javanese', flag: '🇮🇩' },
        'kn': { name: 'Kannada', flag: '🇮🇳' },
        'kk': { name: 'Kazakh', flag: '🇰🇿' },
        'km': { name: 'Khmer', flag: '🇰🇭' },
        'ku': { name: 'Kurdish', flag: '🇮🇶' },
        'ky': { name: 'Kyrgyz', flag: '🇰🇬' },
        'lo': { name: 'Lao', flag: '🇱🇦' },
        'la': { name: 'Latin', flag: '🇻🇦' },
        'lv': { name: 'Latvian', flag: '🇱🇻' },
        'lt': { name: 'Lithuanian', flag: '🇱🇹' },
        'lb': { name: 'Luxembourgish', flag: '🇱🇺' },
        'mk': { name: 'Macedonian', flag: '🇲🇰' },
        'mg': { name: 'Malagasy', flag: '🇲🇬' },
        'ml': { name: 'Malayalam', flag: '🇮🇳' },
        'mt': { name: 'Maltese', flag: '🇲🇹' },
        'mi': { name: 'Maori', flag: '🇳🇿' },
        'mr': { name: 'Marathi', flag: '🇮🇳' },
        'mn': { name: 'Mongolian', flag: '🇲🇳' },
        'my': { name: 'Myanmar (Burmese)', flag: '🇲🇲' },
        'ne': { name: 'Nepali', flag: '🇳🇵' },
        'nb': { name: 'Norwegian Bokmål', flag: '🇳🇴' },
        'nn': { name: 'Norwegian Nynorsk', flag: '🇳🇴' },
        'ps': { name: 'Pashto', flag: '🇦🇫' },
        'fa': { name: 'Persian', flag: '🇮🇷' },
        'pa': { name: 'Punjabi', flag: '🇮🇳' },
        'qu': { name: 'Quechua', flag: '🇵🇪' },
        'sm': { name: 'Samoan', flag: '🇼🇸' },
        'gd': { name: 'Scots Gaelic', flag: '🏴󠁧󠁢󠁳󠁣󠁴󠁿' },
        'sr': { name: 'Serbian', flag: '🇷🇸' },
        'st': { name: 'Sesotho', flag: '🇱🇸' },
        'sn': { name: 'Shona', flag: '🇿🇼' },
        'sd': { name: 'Sindhi', flag: '🇵🇰' },
        'si': { name: 'Sinhala', flag: '🇱🇰' },
        'sk': { name: 'Slovak', flag: '🇸🇰' },
        'sl': { name: 'Slovenian', flag: '🇸🇮' },
        'so': { name: 'Somali', flag: '🇸🇴' },
        'su': { name: 'Sundanese', flag: '🇮🇩' },
        'sw': { name: 'Swahili', flag: '🇹🇿' },
        'tg': { name: 'Tajik', flag: '🇹🇯' },
        'ta': { name: 'Tamil', flag: '🇮🇳' },
        'te': { name: 'Telugu', flag: '🇮🇳' },
        'uz': { name: 'Uzbek', flag: '🇺🇿' },
        'cy': { name: 'Welsh', flag: '🏴󠁧󠁢󠁷󠁬󠁳󠁿' },
        'xh': { name: 'Xhosa', flag: '🇿🇦' },
        'yi': { name: 'Yiddish', flag: '🇮🇱' },
        'yo': { name: 'Yoruba', flag: '🇳🇬' },
        'zu': { name: 'Zulu', flag: '🇿🇦' },

        // Special cases for YouTube
        'en-orig': { name: 'English (Original)', flag: '🇺🇸' },
        'auto': { name: 'Auto-detected', flag: '🌐' }
      }
    };
  },

  computed: {
    // Filter languages based on search input
    filteredLanguages() {
      if (!this.languageSearch) {
        return this.availableLanguages;
      }

      const search = this.languageSearch.toLowerCase();
      return this.availableLanguages.filter(lang =>
        lang.name.toLowerCase().includes(search) ||
        lang.code.toLowerCase().includes(search)
      );
    }
  },

  watch: {
    videoId: {
      immediate: true,
      handler(newVideoId, oldVideoId) {
        console.log('FinalTabsContainer: videoId changed from', oldVideoId, 'to', newVideoId);
        if (newVideoId !== oldVideoId) {
          // Reset selected language when video changes
          this.selectedLanguage = { code: 'en', name: 'English', flag: '🇺🇸' };
          // Fetch transcript for the new video
          console.log('FinalTabsContainer: Fetching transcript for new video:', newVideoId);
          this.fetchTranscript();
        } else {
          console.log('FinalTabsContainer: Same video, fetching transcript:', newVideoId);
          this.fetchTranscript();
        }
      }
    }
  },

  emits: ['seek-to-time', 'subtitles-loading-state', 'language-change'],

  mounted() {
    console.log('FinalTabsContainer mounted with videoId:', this.videoId, 'activeTab:', this.activeTab);
    this.fetchAvailableLanguages();
    this.fetchTranscript();
  },

  beforeUnmount() {
    // Clear any pending debounce timers
    if (this.requestDebounceTimer) {
      clearTimeout(this.requestDebounceTimer);
    }
  },

  methods: {
    setActiveTab(tabId) {
      console.log('Setting active tab to:', tabId);
      this.activeTab = tabId;
    },

    async fetchAvailableLanguages() {
      if (!this.videoId) {
        console.log('No videoId provided, skipping language fetch');
        return;
      }

      try {
        console.log('Fetching available languages for video ID:', this.videoId);
        const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

        // First, set up all languages from our language map
        this.setupCommonLanguages();

        // Get video details to determine the original language
        let originalVideoLanguage = 'en'; // Default to English
        let defaultToEnglish = false;

        try {
          // Fetch video details from the YouTube API
          const videoResponse = await fetch(`${API_URL}/video/${this.videoId}`);
          if (videoResponse.ok) {
            const videoData = await videoResponse.json();
            if (videoData && videoData.snippet && videoData.snippet.defaultAudioLanguage) {
              // Get the original language from the video metadata
              originalVideoLanguage = this.getBaseLanguageCode(videoData.snippet.defaultAudioLanguage);
              console.log(`Video's original language from metadata: ${originalVideoLanguage}`);
            } else if (videoData && videoData.snippet && videoData.snippet.defaultLanguage) {
              // Fallback to default language if audio language is not available
              originalVideoLanguage = this.getBaseLanguageCode(videoData.snippet.defaultLanguage);
              console.log(`Video's default language from metadata: ${originalVideoLanguage}`);
            }
          }
        } catch (videoError) {
          console.error('Error fetching video details:', videoError);
          // Continue with default English if video details can't be fetched
        }

        // Special case handling for known English videos (example videos)
        const knownEnglishVideos = ['TT81fe2IobI', 'arj7oStGLkU', 'Gv2fzC96Z40', 'UF8uR6Z6KLc', 'KpVPST_P4W8', 'ZrN4bKKMlLU'];
        if (knownEnglishVideos.includes(this.videoId)) {
          console.log(`Known English video detected: ${this.videoId}`);
          originalVideoLanguage = 'en';
          defaultToEnglish = true;
        }

        // Then, get the list of actually available languages from the server
        const response = await fetch(`${API_URL}/captions/${this.videoId}`);

        if (!response.ok) {
          console.warn(`HTTP error fetching languages! status: ${response.status}`);

          // If this is a known English video, make sure we default to English
          if (defaultToEnglish) {
            const englishLang = this.availableLanguages.find(lang => lang.baseCode === 'en');
            if (englishLang) {
              console.log('Defaulting to English for known English video');
              this.selectedLanguage = englishLang;
              this.fetchTranscript(englishLang.code);
            }
          } else {
            // Default to English for any video if captions can't be fetched
            const englishLang = this.availableLanguages.find(lang => lang.baseCode === 'en');
            if (englishLang) {
              console.log('Defaulting to English as fallback');
              this.selectedLanguage = englishLang;
              this.fetchTranscript(englishLang.code);
            }
          }

          return; // We already have all languages from setupCommonLanguages
        }

        const captions = await response.json();

        if (Array.isArray(captions)) {
          // Process the captions to extract language information
          const captionLanguages = new Map(); // Use a Map to consolidate languages

          // Process the captions from the API
          captions.forEach(caption => {
            const langCode = caption.snippet.language;
            const baseCode = this.getBaseLanguageCode(langCode);

            // Skip if this is not a valid language code (sometimes YouTube API returns video IDs as language codes)
            if (langCode.length > 10 || !isNaN(langCode)) {
              console.warn(`Skipping invalid language code: ${langCode}`);
              return;
            }

            const langInfo = this.languageMap[langCode] || this.languageMap[baseCode] || { name: caption.snippet.language, flag: '🌐' };

            const language = {
              code: langCode,
              baseCode: baseCode,
              name: langInfo.name,
              flag: langInfo.flag,
              trackKind: caption.snippet.trackKind,
              captionId: caption.id,
              isAvailable: true, // Mark as actually available
              isOriginalLanguage: baseCode === originalVideoLanguage // Mark if this is the original language
            };

            // Special handling for English to avoid duplicates showing different languages
            if (baseCode === 'en') {
              // If we already have English, only replace if this is a manual caption
              if (captionLanguages.has('en')) {
                const existing = captionLanguages.get('en');
                if (existing.trackKind !== 'standard' && language.trackKind === 'standard') {
                  captionLanguages.set('en', language);
                }
              } else {
                captionLanguages.set('en', language);
              }
            }
            // For other languages, use the same logic
            else if (captionLanguages.has(baseCode)) {
              const existing = captionLanguages.get(baseCode);
              if (existing.trackKind !== 'standard' && language.trackKind === 'standard') {
                captionLanguages.set(baseCode, language);
              }
            } else {
              captionLanguages.set(baseCode, language);
            }
          });

          // Mark languages that are actually available in the video
          if (captionLanguages.size > 0) {
            // Update our availableLanguages to mark which ones are actually available
            this.availableLanguages = this.availableLanguages.map(lang => {
              const captionLang = captionLanguages.get(lang.baseCode);
              if (captionLang) {
                return {
                  ...lang,
                  isAvailable: true,
                  trackKind: captionLang.trackKind,
                  captionId: captionLang.captionId,
                  isOriginalLanguage: captionLang.isOriginalLanguage
                };
              }
              return lang;
            });

            console.log('Updated available languages with caption information');

            // Find the original language of the video with improved prioritization
            // Priority:
            // 1. Manual captions in the original language of the video
            // 2. Auto-generated captions in the original language
            // 3. Manual captions in any language
            // 4. Any available language
            // 5. English as fallback
            let originalLang;

            // If this is a known English video, force English
            if (defaultToEnglish) {
              originalLang = this.availableLanguages.find(lang =>
                lang.baseCode === 'en' && lang.isAvailable && lang.trackKind === 'standard'
              ) || this.availableLanguages.find(lang =>
                lang.baseCode === 'en' && lang.isAvailable
              );

              if (originalLang) {
                console.log('Using English for known English video:', originalLang.name);
              }
            }

            // If not forced or no English found, use the normal prioritization
            if (!originalLang) {
              originalLang = this.availableLanguages.find(lang =>
                lang.isAvailable && lang.isOriginalLanguage && lang.trackKind === 'standard'
              ) || this.availableLanguages.find(lang =>
                lang.isAvailable && lang.isOriginalLanguage
              ) || this.availableLanguages.find(lang =>
                lang.isAvailable && lang.trackKind === 'standard'
              ) || this.availableLanguages.find(lang =>
                lang.isAvailable
              ) || this.availableLanguages.find(lang =>
                lang.baseCode === 'en'
              ) || this.availableLanguages[0];
            }

            console.log('Setting selected language to original language:', originalLang.name);
            this.selectedLanguage = originalLang;

            // Emit the language change to parent component
            this.$emit('language-change', originalLang);

            // Force a transcript fetch with the original language
            this.fetchTranscript(originalLang.code);
          } else {
            // No available languages found, default to English
            const englishLang = this.availableLanguages.find(lang => lang.baseCode === 'en');
            if (englishLang) {
              console.log('No available languages found, defaulting to English');
              this.selectedLanguage = englishLang;
              this.fetchTranscript(englishLang.code);
            }
          }
        } else {
          console.warn('Invalid captions data format:', captions);

          // If this is a known English video, make sure we default to English
          if (defaultToEnglish) {
            const englishLang = this.availableLanguages.find(lang => lang.baseCode === 'en');
            if (englishLang) {
              console.log('Defaulting to English for known English video');
              this.selectedLanguage = englishLang;
              this.fetchTranscript(englishLang.code);
            }
          } else {
            // Default to English for any video if captions can't be fetched
            const englishLang = this.availableLanguages.find(lang => lang.baseCode === 'en');
            if (englishLang) {
              console.log('Defaulting to English as fallback');
              this.selectedLanguage = englishLang;
              this.fetchTranscript(englishLang.code);
            }
          }
        }
      } catch (err) {
        console.error('Error fetching available languages:', err);
        // We already have all languages from setupCommonLanguages

        // Default to English for any video if there's an error
        const englishLang = this.availableLanguages.find(lang => lang.baseCode === 'en');
        if (englishLang) {
          console.log('Error occurred, defaulting to English');
          this.selectedLanguage = englishLang;
          this.fetchTranscript(englishLang.code);
        }
      }
    },

    // This method is no longer used, but kept for compatibility
    async probeAvailableLanguages() {
      console.log('Using setupCommonLanguages instead of probing');
      this.setupCommonLanguages();
    },

    // Get the base language code (e.g., 'en' from 'en-US')
    getBaseLanguageCode(code) {
      if (!code) return 'en';

      // Handle special cases
      if (code === 'en-orig') return 'en';
      if (code === 'auto') return 'en';

      // Handle special cases like 'en-orig'
      if (code.endsWith('-orig')) {
        return code.split('-')[0];
      }

      // Extract base code (e.g., 'en' from 'en-US')
      const baseCode = code.split('-')[0];

      // Special case for Chinese
      if (code.startsWith('zh')) {
        if (code.includes('Hans')) return 'zh-Hans';
        if (code.includes('Hant')) return 'zh-Hant';
        return 'zh';
      }

      return baseCode;
    },

    // Set up all available languages
    setupCommonLanguages() {
      // Create an array of all languages from the languageMap
      const allLanguages = Object.entries(this.languageMap).map(([code, info]) => {
        return {
          code: code,
          baseCode: this.getBaseLanguageCode(code),
          name: info.name,
          flag: info.flag,
          trackKind: 'auto-generated',
          captionId: null,
          isAvailable: false
        };
      });

      // Filter out duplicates based on baseCode (keeping the first occurrence)
      const uniqueLanguages = [];
      const seenBaseCodes = new Set();

      for (const lang of allLanguages) {
        // Skip regional variants for cleaner dropdown
        if (lang.code.includes('-') && lang.code !== 'zh-Hans' && lang.code !== 'zh-Hant' &&
            lang.code !== 'en-US' && lang.code !== 'en-orig') {
          continue;
        }

        // Special handling for English variants to avoid duplicates
        if (lang.baseCode === 'en') {
          // Only add the main English entry if we haven't seen it yet
          if (!seenBaseCodes.has('en')) {
            seenBaseCodes.add('en');
            uniqueLanguages.push(lang);
          }
        } else if (!seenBaseCodes.has(lang.baseCode)) {
          seenBaseCodes.add(lang.baseCode);
          uniqueLanguages.push(lang);
        }
      }

      // Sort languages alphabetically by name
      uniqueLanguages.sort((a, b) => a.name.localeCompare(b.name));

      // Move English to the top
      const englishIndex = uniqueLanguages.findIndex(lang => lang.baseCode === 'en');
      if (englishIndex > 0) {
        const english = uniqueLanguages.splice(englishIndex, 1)[0];
        uniqueLanguages.unshift(english);
      }

      this.availableLanguages = uniqueLanguages;
      this.selectedLanguage = uniqueLanguages[0]; // Default to English
    },

    async changeLanguage(langCode) {
      if (langCode === this.selectedLanguage.code) {
        return; // Already selected
      }

      console.log(`Changing language to: ${langCode}`);
      const langInfo = this.availableLanguages.find(lang => lang.code === langCode);

      if (langInfo) {
        // Set the selected language immediately to prevent UI confusion
        this.selectedLanguage = langInfo;

        // Emit the language change to parent component
        this.$emit('language-change', langInfo);

        // Use the fetchTranscript method with the language code to get the transcript
        // This ensures consistent handling of language selection
        await this.fetchTranscript(langCode);
      }
    },

    async fetchTranscript(langCode = null) {
      if (!this.videoId) {
        console.log('No videoId provided, skipping transcript fetch');
        return;
      }

      // Debounce requests to prevent rapid-fire calls
      if (this.requestDebounceTimer) {
        clearTimeout(this.requestDebounceTimer);
      }

      // Prevent duplicate requests for the same video and language
      const requestKey = `${this.videoId}-${langCode || 'default'}`;
      if (this.currentRequest === requestKey) {
        console.log('Duplicate request detected, skipping:', requestKey);
        return;
      }

      // Set a small debounce delay to prevent rapid requests
      this.requestDebounceTimer = setTimeout(async () => {
        await this.performTranscriptFetch(langCode, requestKey);
      }, 100);
    },

    async performTranscriptFetch(langCode, requestKey) {
      this.currentRequest = requestKey;

      // Store whether this is a new video load or a language change
      const isNewVideoLoad = !langCode;

      // When a new video is loaded, fetch available languages first
      if (isNewVideoLoad) {
        await this.fetchAvailableLanguages();
        // Note: fetchAvailableLanguages will call fetchTranscript with the original language
        // so we can return early to avoid duplicate fetching
        this.currentRequest = null;
        return;
      }

      this.loading = true;
      this.error = '';

      // Emit that subtitles are loading
      this.$emit('subtitles-loading-state', true);

      try {
        // If a specific language code is provided, use it directly
        // Otherwise use the currently selected language
        const language = langCode || (this.selectedLanguage && this.selectedLanguage.code) || 'en';
        console.log(`Fetching transcript for video ID: ${this.videoId} in language: ${language}`);

        // Use environment variable for API URL, with fallback
        const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

        // Add a cache-busting parameter to prevent caching when changing languages
        const timestamp = new Date().getTime();
        const response = await fetch(`${API_URL}/transcript/${this.videoId}?lang=${language}&_t=${timestamp}`);

        if (!response.ok) {
          console.warn(`HTTP error fetching transcript! status: ${response.status}`);

          // Try again with English as a fallback
          if (language !== 'en') {
            console.log('Trying English as fallback...');
            const englishResponse = await fetch(`${API_URL}/transcript/${this.videoId}?lang=en&_t=${timestamp}`);

            if (englishResponse.ok) {
              const englishData = await englishResponse.json();
              if (englishData && englishData.transcript && Array.isArray(englishData.transcript)) {
                console.log('Successfully fetched English transcript as fallback');
                this.transcript = englishData.transcript;

                // Update selected language to English
                const englishLang = this.availableLanguages.find(lang => lang.baseCode === 'en');
                if (englishLang) {
                  this.selectedLanguage = englishLang;
                }

                this.error = `Subtitles in ${this.getLanguageName(language)} were not available. Showing English subtitles instead.`;
                return;
              }
            }
          }

          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Transcript API response received:', {
          videoId: this.videoId,
          language: language,
          hasTranscript: !!(data && data.transcript),
          transcriptLength: data && data.transcript ? data.transcript.length : 0,
          isError: data && data.isError
        });

        // Check if the transcript has an error flag
        if (data.isError) {
          console.warn('Transcript has error flag:', data.error);
          this.error = data.error || 'Failed to fetch transcript';

          // Still update the transcript data to show the error message
          this.transcript = data.transcript || [];

          // Try English as a fallback if not already trying English
          if (language !== 'en') {
            console.log('Trying English as fallback due to error...');
            try {
              const englishResponse = await fetch(`${API_URL}/transcript/${this.videoId}?lang=en&_t=${timestamp}`);

              if (englishResponse.ok) {
                const englishData = await englishResponse.json();
                if (englishData && !englishData.isError && englishData.transcript && Array.isArray(englishData.transcript)) {
                  console.log('Successfully fetched English transcript as fallback');
                  this.transcript = englishData.transcript;

                  // Update selected language to English
                  const englishLang = this.availableLanguages.find(lang => lang.baseCode === 'en');
                  if (englishLang) {
                    this.selectedLanguage = englishLang;
                  }

                  this.error = `Subtitles in ${this.getLanguageName(language)} were not available. Showing English subtitles instead.`;
                }
              }
            } catch (fallbackError) {
              console.error('Error fetching English fallback:', fallbackError);
            }
          }

          // Emit that subtitles are loaded with error
          this.$emit('subtitles-loading-state', false);
          return;
        }

        // Check for fallback language
        if (data.fallbackLanguage) {
          console.warn(`Using fallback language ${data.fallbackLanguage} instead of requested ${data.language}`);
          this.error = `Subtitles in ${this.getLanguageName(data.language)} were not available. Showing ${this.getLanguageName(data.fallbackLanguage)} subtitles instead.`;
        }

        if (data && data.transcript && Array.isArray(data.transcript)) {
          console.log('Transcript found, length:', data.transcript.length, 'language:', data.language || language, 'auto-generated:', data.isAutoGenerated);
          this.transcript = data.transcript;

          // Always update the selected language to match the actual language of the transcript
          if (data.language) {
            // Store the auto-generated flag
            const isAutoGenerated = data.isAutoGenerated || false;
            console.log(`Server returned transcript in language: ${data.language}`);

            // Find the language in our available languages
            const returnedLangInfo = this.availableLanguages.find(lang =>
              lang.code === data.language ||
              lang.baseCode === data.language ||
              this.getBaseLanguageCode(lang.code) === this.getBaseLanguageCode(data.language)
            );

            if (returnedLangInfo) {
              console.log(`Found matching language in available languages: ${returnedLangInfo.name}`);
              // Update the language with auto-generated flag if needed
              returnedLangInfo.isAutoGenerated = isAutoGenerated;
              this.selectedLanguage = returnedLangInfo;
            } else {
              // If we can't find the exact language, try to find a language with the same base code
              const baseCode = this.getBaseLanguageCode(data.language);
              const similarLang = this.availableLanguages.find(lang =>
                this.getBaseLanguageCode(lang.code) === baseCode
              );

              if (similarLang) {
                console.log(`Found similar language with base code ${baseCode}: ${similarLang.name}`);
                // Update the language with auto-generated flag if needed
                similarLang.isAutoGenerated = isAutoGenerated;
                this.selectedLanguage = similarLang;
              } else if (this.languageMap[data.language]) {
                // If we have the language in our map but not in available languages, add it
                const langInfo = this.languageMap[data.language];
                const newLang = {
                  code: data.language,
                  baseCode: this.getBaseLanguageCode(data.language),
                  name: langInfo.name,
                  flag: langInfo.flag,
                  trackKind: 'auto-generated',
                  captionId: null,
                  isAutoGenerated: isAutoGenerated
                };
                this.availableLanguages.push(newLang);
                this.selectedLanguage = newLang;
                console.log(`Added new language to available languages: ${newLang.name}`);
              } else {
                // Try to determine a proper language name based on the code
                let languageName = 'Unknown Language';
                let languageFlag = '🌐';

                // Check if it's a video ID that got mixed in somehow
                if (data.language === 'KpVPST_P4W8') {
                  languageName = 'English';
                  languageFlag = '🇺🇸';
                } else if (data.language === 'arj7oStGLkU') {
                  languageName = 'English';
                  languageFlag = '🇺🇸';
                } else if (data.language === 'TT81fe2IobI') {
                  languageName = 'English';
                  languageFlag = '🇺🇸';
                } else if (data.language === 'UF8uR6Z6KLc') {
                  languageName = 'English';
                  languageFlag = '🇺🇸';
                } else if (data.language === 'Gv2fzC96Z40') {
                  languageName = 'English';
                  languageFlag = '🇺🇸';
                } else {
                  // Try to map common language codes
                  const langCodeMap = {
                    'en': 'English',
                    'fr': 'French',
                    'de': 'German',
                    'es': 'Spanish',
                    'it': 'Italian',
                    'pt': 'Portuguese',
                    'ru': 'Russian',
                    'ja': 'Japanese',
                    'ko': 'Korean',
                    'zh': 'Chinese',
                    'ar': 'Arabic',
                    'hi': 'Hindi',
                    'nl': 'Dutch'
                  };

                  // Check if we have a mapping for this language code
                  const baseCode = this.getBaseLanguageCode(data.language);
                  if (langCodeMap[baseCode]) {
                    languageName = langCodeMap[baseCode];
                    // Use the flag from our language map if available
                    if (this.languageMap[baseCode]) {
                      languageFlag = this.languageMap[baseCode].flag;
                    }
                  }
                }

                const newLang = {
                  code: data.language,
                  baseCode: this.getBaseLanguageCode(data.language),
                  name: languageName,
                  flag: languageFlag,
                  trackKind: 'auto-generated',
                  captionId: null,
                  isAutoGenerated: isAutoGenerated
                };
                this.availableLanguages.push(newLang);
                this.selectedLanguage = newLang;
                console.log(`Added generic language to available languages: ${newLang.name}`);
              }
            }
          }
        } else {
          console.error('Invalid transcript data format:', data);
          this.error = 'Invalid transcript data format';

          // Try English as a fallback if not already trying English
          if (language !== 'en') {
            console.log('Trying English as fallback due to invalid format...');
            try {
              const englishResponse = await fetch(`${API_URL}/transcript/${this.videoId}?lang=en&_t=${timestamp}`);

              if (englishResponse.ok) {
                const englishData = await englishResponse.json();
                if (englishData && englishData.transcript && Array.isArray(englishData.transcript)) {
                  console.log('Successfully fetched English transcript as fallback');
                  this.transcript = englishData.transcript;

                  // Update selected language to English
                  const englishLang = this.availableLanguages.find(lang => lang.baseCode === 'en');
                  if (englishLang) {
                    this.selectedLanguage = englishLang;
                  }

                  this.error = `Subtitles in ${this.getLanguageName(language)} were not available. Showing English subtitles instead.`;
                  return;
                }
              }
            } catch (fallbackError) {
              console.error('Error fetching English fallback:', fallbackError);
            }
          }

          // If all else fails, use a simple placeholder transcript
          this.transcript = [
            { id: 1, start: 0, end: 5, formattedStart: "00:00", formattedEnd: "00:05", text: "No transcript data available for this video." },
            { id: 2, start: 5, end: 10, formattedStart: "00:05", formattedEnd: "00:10", text: "Try selecting a different language or video." }
          ];
        }
      } catch (err) {
        console.error('Error fetching transcript:', err);
        this.error = `Failed to fetch transcript: ${err.message}`;

        // Try English as a fallback if not already trying English
        if (langCode !== 'en') {
          console.log('Trying English as fallback after error...');
          try {
            const timestamp = new Date().getTime();
            const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';
            const englishResponse = await fetch(`${API_URL}/transcript/${this.videoId}?lang=en&_t=${timestamp}`);

            if (englishResponse.ok) {
              const englishData = await englishResponse.json();
              if (englishData && englishData.transcript && Array.isArray(englishData.transcript)) {
                console.log('Successfully fetched English transcript as fallback');
                this.transcript = englishData.transcript;

                // Update selected language to English
                const englishLang = this.availableLanguages.find(lang => lang.baseCode === 'en');
                if (englishLang) {
                  this.selectedLanguage = englishLang;
                }

                this.error = `Subtitles in ${this.getLanguageName(langCode)} were not available. Showing English subtitles instead.`;
                return;
              }
            }
          } catch (fallbackError) {
            console.error('Error fetching English fallback:', fallbackError);
          }
        }

        // If all else fails, use a simple placeholder transcript
        this.transcript = [
          { id: 1, start: 0, end: 5, formattedStart: "00:00", formattedEnd: "00:05", text: "No transcript data available for this video." },
          { id: 2, start: 5, end: 10, formattedStart: "00:05", formattedEnd: "00:10", text: "Try selecting a different language or video." }
        ];
      } finally {
        this.loading = false;
        this.currentRequest = null; // Clear the current request

        // Emit that subtitles are loaded
        this.$emit('subtitles-loading-state', false);
      }
    },

    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      return [
        hours.toString().padStart(2, '0'),
        minutes.toString().padStart(2, '0'),
        secs.toString().padStart(2, '0')
      ].join(':');
    },

    getLanguageName(langCode) {
      if (!langCode) return 'Unknown';

      // First check if we have this language in our available languages
      const langInfo = this.availableLanguages.find(lang =>
        lang.code === langCode ||
        lang.baseCode === langCode ||
        this.getBaseLanguageCode(lang.code) === this.getBaseLanguageCode(langCode)
      );

      if (langInfo) {
        return langInfo.name;
      }

      // Check if we have it in our language map
      if (this.languageMap && this.languageMap[langCode]) {
        return this.languageMap[langCode].name;
      }

      // Try to map common language codes
      const langCodeMap = {
        'en': 'English',
        'fr': 'French',
        'de': 'German',
        'es': 'Spanish',
        'it': 'Italian',
        'pt': 'Portuguese',
        'ru': 'Russian',
        'ja': 'Japanese',
        'ko': 'Korean',
        'zh': 'Chinese',
        'ar': 'Arabic',
        'hi': 'Hindi',
        'nl': 'Dutch'
      };

      // Check if we have a mapping for this language code
      const baseCode = this.getBaseLanguageCode(langCode);
      if (langCodeMap[baseCode]) {
        return langCodeMap[baseCode];
      }

      return langCode; // Return the code itself if we can't find a name
    },

    // This method has been consolidated with the one above

    seekToTime(time) {
      console.log(`FinalTabsContainer: Seeking to time ${time} for video ${this.videoId}`);
      // Emit the seek-to-time event with a flag to force autoplay
      this.$emit('seek-to-time', time, true);
    }
  }
};
</script>

<style scoped>
.tabs-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-buttons {
  display: flex;
  flex-wrap: wrap;
}

.tab-button {
  background-color: #f0f0f0;
  transition: all 0.2s ease;
  cursor: pointer;
  color: #333;
}

.tab-button:hover {
  background-color: #e0e0e0;
}

.tab-button.active-tab {
  background-color: #4f46e5;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

.tab-content-container {
  flex: 1;
  position: relative;
}

.transcript-container, .coming-soon-container {
  height: 100%;
}

.coming-soon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem;
}

.transcript-list {
  height: 450px;
  overflow-y: auto;
  border-radius: 0.5rem;
}

table {
  border-collapse: separate;
  border-spacing: 0;
}

tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

td {
  padding: 0.75rem 0.5rem;
}

td:first-child button {
  transition: all 0.2s ease;
}

td:first-child button:hover {
  background-color: #4f46e5;
  color: white;
}

/* Language dropdown styles */
.menu li a {
  display: flex;
  width: 100%;
  padding: 0.5rem 1rem;
  cursor: pointer;
}

.menu li:hover a {
  background-color: inherit;
}

/* Search input container */
.dropdown-content .relative {
  width: 100%;
}

/* Ensure consistent padding */
.dropdown-content .input {
  box-sizing: border-box;
  width: 100%;
}

/* Language dropdown styling */
.language-dropdown .menu {
  width: 100%;
  padding: 0;
}

.language-dropdown .menu li {
  width: 100%;
}

/* Custom loading bar animation */
@keyframes loadingBar {
  0% {
    width: 0%;
    margin-left: 0;
  }
  50% {
    width: 100%;
    margin-left: 0;
  }
  100% {
    width: 0%;
    margin-left: 100%;
  }
}

.language-dropdown .menu li a {
  width: 100%;
  box-sizing: border-box;
  padding: 0.5rem 1rem;
}
</style>
