<template>
  <div>
    <div v-if="loading || waitingForSubtitles" class="flex flex-col justify-center items-center py-12 space-y-4">
      <!-- Modern spinning loader -->
      <div class="relative">
        <div class="w-20 h-20 flex items-center justify-center">
          <svg class="animate-spin h-12 w-12 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      </div>
      <p class="text-base-content/70 font-medium">
        {{ waitingForSubtitles ? 'Loading subtitles...' : 'Loading video...' }}
      </p>
    </div>

    <div v-else-if="error" class="alert alert-error">
      <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
      <span>{{ error }}</span>
    </div>

    <div v-else-if="videoInfo" class="space-y-4">
      <h3 class="text-lg font-bold line-clamp-2">{{ videoInfo.snippet.title }}</h3>
      <div class="relative">
        <!-- Simple iframe approach -->
        <div class="relative pb-[56.25%] h-0 overflow-hidden rounded-lg">
          <iframe
            ref="videoIframe"
            :key="videoId"
            :src="`https://www.youtube.com/embed/${videoId}?enablejsapi=1&autoplay=0&origin=${origin}&rel=0&modestbranding=1&fs=1&playsinline=1&widgetid=1`"
            class="absolute top-0 left-0 w-full h-full"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
          ></iframe>
        </div>
        <div
          v-if="currentTime > 0"
          class="absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded font-medium text-sm"
        >
          {{ formatTime(currentTime) }}
        </div>
      </div>
    </div>

    <div v-else class="alert">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-info shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
      <span>No video selected. Please select a video to view.</span>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue';
import api from '../services/api';

const props = defineProps({
  videoId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['time-update']);

const loading = ref(false);
const error = ref('');
const videoInfo = ref(null);
const videoIframe = ref(null);
const currentTime = ref(0);
const timeUpdateInterval = ref(null);
const origin = window.location.origin;
const waitingForSubtitles = ref(true); // Start with waiting for subtitles

// Set up message listener for YouTube iframe API
onMounted(() => {
  window.addEventListener('message', handleYouTubeMessage);

  // Start time update interval
  startTimeUpdateInterval();

  // Initialize the player after a short delay to ensure iframe is loaded
  setTimeout(initializePlayer, 1000);
});

// Initialize the YouTube player
function initializePlayer() {
  if (!videoIframe.value || !videoIframe.value.contentWindow) {
    console.warn('VideoPreview: Video iframe not available for initialization');
    return;
  }

  try {
    // Send initial listening message to activate the API
    videoIframe.value.contentWindow.postMessage({
      event: 'listening',
      id: 1,
      channel: 'widget'
    }, '*');

    // Also try the older format
    videoIframe.value.contentWindow.postMessage(JSON.stringify({
      event: 'listening'
    }), '*');

    console.log('VideoPreview: Sent initialization messages to player');
  } catch (error) {
    console.error('VideoPreview: Error initializing player:', error);
  }
}

// Clean up on unmount
onUnmounted(() => {
  window.removeEventListener('message', handleYouTubeMessage);

  if (timeUpdateInterval.value) {
    clearInterval(timeUpdateInterval.value);
  }
});

// Handle messages from YouTube iframe
function handleYouTubeMessage(event) {
  // Process messages from YouTube (could be from www.youtube.com or youtube.com)
  if (!event.origin.includes('youtube.com')) return;

  try {
    // Try to parse as JSON
    const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;

    // Handle different message types
    if (data.event === 'onStateChange') {
      // State change event
      if (data.info === 1) { // Playing
        console.log('VideoPreview: Video is playing');
        startTimeUpdateInterval();
      } else if (data.info === 2) { // Paused
        console.log('VideoPreview: Video is paused');
      }
    } else if (data.event === 'infoDelivery' && data.info && data.info.currentTime) {
      // Time update event
      currentTime.value = data.info.currentTime;
      emit('time-update', currentTime.value);
    } else if (data.event === 'initialDelivery') {
      console.log('VideoPreview: Player ready for commands');
    }
  } catch (error) {
    // Not a JSON message or other error, ignore
  }
}

// Start the interval to update current time
function startTimeUpdateInterval() {
  // Clear any existing interval first
  if (timeUpdateInterval.value) {
    clearInterval(timeUpdateInterval.value);
  }

  // Start a new interval that uses postMessage to get the current time
  timeUpdateInterval.value = setInterval(() => {
    if (videoIframe.value && videoIframe.value.contentWindow) {
      try {
        // Request current time from YouTube player using the correct format
        videoIframe.value.contentWindow.postMessage({
          event: 'listening',
          id: 1,
          channel: 'widget'
        }, '*');

        // Also request current time in the old format as a fallback
        videoIframe.value.contentWindow.postMessage(JSON.stringify({
          event: 'command',
          func: 'getCurrentTime',
          args: []
        }), '*');
      } catch (error) {
        console.error('VideoPreview: Error requesting current time:', error);
      }
    }
  }, 500);
}

// Watch for video ID changes
watch(() => props.videoId, (newId) => {
  console.log(`VideoPreview: Video ID changed to ${newId}`);
  // Reset current time when video changes
  currentTime.value = 0;
  // Reset subtitle loading state
  waitingForSubtitles.value = true;
  fetchVideoInfo();

  // Initialize the player after a short delay to ensure iframe is loaded with the new video
  setTimeout(initializePlayer, 1000);
}, { immediate: true });

// Format time in HH:MM:SS format
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

// Fetch video information
async function fetchVideoInfo() {
  if (!props.videoId) return;

  loading.value = true;
  error.value = '';

  try {
    const response = await api.getVideoInfo(props.videoId);
    videoInfo.value = response.data;
  } catch (err) {
    console.error('VideoPreview: Error fetching video info:', err);
    error.value = err.response?.data?.error || 'Failed to fetch video information';
  } finally {
    loading.value = false;
  }
}

// Seek to a specific time in the video
function seekToTime(time, forcePlay = false) {
  console.log(`VideoPreview: Seeking to ${time} seconds in video ${props.videoId}, forcePlay: ${forcePlay}`);

  if (!videoIframe.value) {
    console.warn('VideoPreview: Video iframe not available');
    return;
  }

  // Try multiple approaches to seek to ensure it works
  try {
    // First approach: Use the YouTube iframe API format
    videoIframe.value.contentWindow.postMessage({
      event: 'command',
      func: 'seekTo',
      args: [parseFloat(time), true],
      id: 1,
      channel: 'widget'
    }, '*');

    // Second approach: Use the JSON string format
    videoIframe.value.contentWindow.postMessage(JSON.stringify({
      event: 'command',
      func: 'seekTo',
      args: [parseFloat(time), true]
    }), '*');

    // Third approach: Use the older format
    videoIframe.value.contentWindow.postMessage('{"event":"command","func":"seekTo","args":[' + parseFloat(time) + ',true]}', '*');

    // Always send play command when forcePlay is true or when clicking on timestamps
    if (forcePlay) {
      console.log('VideoPreview: Force playing video after seeking');

      // Send play command in multiple formats to ensure it works
      videoIframe.value.contentWindow.postMessage({
        event: 'command',
        func: 'playVideo',
        args: [],
        id: 1,
        channel: 'widget'
      }, '*');

      videoIframe.value.contentWindow.postMessage(JSON.stringify({
        event: 'command',
        func: 'playVideo',
        args: []
      }), '*');

      videoIframe.value.contentWindow.postMessage('{"event":"command","func":"playVideo","args":[]}', '*');
    }

    console.log(`VideoPreview: Sent seek commands to ${time} seconds`);
  } catch (error) {
    console.error('VideoPreview: Error seeking with postMessage:', error);

    // Fallback: If all else fails, reload the iframe with the timestamp
    fallbackReloadWithTimestamp(time, forcePlay);
  }
}

// Fallback method: Reload the iframe with a specific timestamp
function fallbackReloadWithTimestamp(time, forcePlay = false) {
  if (!videoIframe.value) return;

  try {
    const currentSrc = videoIframe.value.src;
    // Remove any existing start parameter
    const baseUrl = currentSrc.replace(/&start=\d+/, '');
    // Add the new start parameter and autoplay if forcePlay is true
    const autoplayParam = forcePlay ? '&autoplay=1' : '';
    videoIframe.value.src = `${baseUrl}&start=${Math.floor(time)}${autoplayParam}`;
    console.log(`VideoPreview: Fallback - Reloaded iframe with start time ${time}, autoplay: ${forcePlay}`);
  } catch (error) {
    console.error('VideoPreview: Error in fallback reload:', error);
  }
}

// Method to play the video when subtitles are loaded
function playVideoWhenReady() {
  if (!videoIframe.value || !videoIframe.value.contentWindow) {
    console.warn('VideoPreview: Video iframe not available for playback');
    return;
  }

  console.log('VideoPreview: Playing video after subtitles loaded');
  waitingForSubtitles.value = false;

  try {
    // Send play command in multiple formats to ensure it works
    videoIframe.value.contentWindow.postMessage({
      event: 'command',
      func: 'playVideo',
      args: [],
      id: 1,
      channel: 'widget'
    }, '*');

    videoIframe.value.contentWindow.postMessage(JSON.stringify({
      event: 'command',
      func: 'playVideo',
      args: []
    }), '*');

    videoIframe.value.contentWindow.postMessage('{"event":"command","func":"playVideo","args":[]}', '*');
  } catch (error) {
    console.error('VideoPreview: Error playing video:', error);
  }
}

// Method to set subtitle loading state
function setSubtitleLoadingState(isLoading) {
  waitingForSubtitles.value = isLoading;

  // If subtitles are loaded, play the video
  if (!isLoading) {
    playVideoWhenReady();
  }
}

// Expose methods to parent components
defineExpose({ seekToTime, setSubtitleLoadingState });
</script>

<style scoped>
.video-preview {
  max-width: 800px;
  margin: 20px auto;
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading, .error, .no-video {
  text-align: center;
  padding: 20px;
}

.error {
  color: #ff0000;
}

/* Basic iframe styling */
:deep(iframe) {
  border: none !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
}

/* YouTube iframe specific styling - most fixes moved to youtube-fixes.css */
:deep(iframe[src*="youtube.com"]) {
  /* Ensure proper sizing and positioning */
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;

  /* Prevent any potential CSS conflicts */
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;

  /* Additional fixes for vendor-specific properties */
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  -ms-transition: none !important;
  transition: none !important;

  -webkit-transform: none !important;
  -moz-transform: none !important;
  -o-transform: none !important;
  -ms-transform: none !important;
  transform: none !important;

  -webkit-animation: none !important;
  -moz-animation: none !important;
  -o-animation: none !important;
  -ms-animation: none !important;
  animation: none !important;
}

/* Ensure the iframe container has proper z-index to prevent issues */
.relative {
  position: relative;
  z-index: 1;

  /* Prevent any CSS conflicts with YouTube iframe */
  overflow: hidden !important;
  border-radius: 0.5rem !important;
}</style>
