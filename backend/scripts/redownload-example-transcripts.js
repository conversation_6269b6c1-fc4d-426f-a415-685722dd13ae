const fs = require('fs');
const path = require('path');
const transcriptService = require('../services/transcriptService');

// List of example video IDs
const exampleVideoIds = [
  'TT81fe2IobI',
  'arj7oStGLkU', 
  'Gv2fzC96Z40',
  'UF8uR6Z6KLc',
  'ZrN4bKKMlLU'
];

async function redownloadExampleTranscripts() {
  console.log('🔄 Starting redownload of example video transcripts...');

  for (const videoId of exampleVideoIds) {
    try {
      console.log(`\n📹 Processing video: ${videoId}`);

      // Remove existing cached transcript if it exists
      const cacheFilePath = path.join(__dirname, '../cache', `${videoId}.json`);
      if (fs.existsSync(cacheFilePath)) {
        console.log(`🗑️  Removing existing cache file: ${cacheFilePath}`);
        fs.unlinkSync(cacheFilePath);
      }

      // Also remove any pre-cached transcript files
      const preCacheFilePath = path.join(__dirname, '../data/cached-transcripts', `${videoId}.json`);
      if (fs.existsSync(preCacheFilePath)) {
        console.log(`🗑️  Removing existing pre-cache file: ${preCacheFilePath}`);
        fs.unlinkSync(preCacheFilePath);
      }

      // Force fresh download in English bypassing all caches
      console.log(`⬇️  Downloading fresh English transcript for ${videoId}...`);

      // Use forceFresh parameter to bypass all caching
      const transcript = await transcriptService.getTranscript(videoId, 'en', true);

      if (transcript && transcript.transcript && transcript.transcript.length > 0) {
        console.log(`✅ Successfully downloaded transcript for ${videoId}`);
        console.log(`   - Language: ${transcript.language}`);
        console.log(`   - Items: ${transcript.transcript.length}`);
        console.log(`   - Auto-generated: ${transcript.isAutoGenerated || false}`);

        // Verify the transcript has proper content
        const sampleText = transcript.transcript.slice(0, 3).map(item => item.text).join(' ');
        console.log(`   - Sample text: ${sampleText.substring(0, 100)}...`);

        // Save to cache manually
        const cacheData = {
          videoId: videoId,
          language: transcript.language,
          transcript: transcript.transcript,
          isAutoGenerated: transcript.isAutoGenerated || false,
          cachedAt: new Date().toISOString()
        };

        fs.writeFileSync(cacheFilePath, JSON.stringify(cacheData, null, 2));
        console.log(`💾 Saved fresh transcript to cache: ${cacheFilePath}`);

      } else {
        console.error(`❌ Failed to download transcript for ${videoId}`);
      }

      // Add a small delay between downloads to be respectful
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      console.error(`❌ Error processing ${videoId}:`, error.message);
    }
  }

  console.log('\n🎉 Finished redownloading example video transcripts!');
}

// Run the script
if (require.main === module) {
  redownloadExampleTranscripts()
    .then(() => {
      console.log('✅ Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { redownloadExampleTranscripts };
