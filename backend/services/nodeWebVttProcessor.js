/**
 * Node WebVTT Processor
 *
 * Uses the node-webvtt package to properly parse VTT files
 * and handle different languages correctly.
 */

const webvtt = require('node-webvtt');
const he = require('he');

/**
 * Process a VTT file content using node-webvtt
 * @param {string} vttContent - Raw VTT file content
 * @param {string} lang - Language code
 * @returns {Array} Processed transcript items
 */
function processVttContent(vttContent, lang) {
  console.log(`Processing VTT content for language: ${lang} using node-webvtt`);

  try {
    // Parse the VTT content using node-webvtt
    // Set strict to false to handle potential parsing errors
    const parsed = webvtt.parse(vttContent, { strict: false, meta: true });

    if (!parsed || !parsed.cues || parsed.cues.length === 0) {
      console.error('No cues found in VTT content');
      return [];
    }

    console.log(`Parsed ${parsed.cues.length} cues from VTT file`);

    // Convert the parsed cues to our transcript format
    const transcriptItems = parsed.cues.map((cue, index) => {
      // Decode HTML entities in the text
      let text = decodeHtmlEntities(cue.text);

      return {
        id: index + 1,
        start: cue.start,
        end: cue.end,
        formattedStart: formatTime(cue.start),
        formattedEnd: formatTime(cue.end),
        text: text
      };
    });

    // Check if the transcript contains Arabic text when we didn't request Arabic
    // But only for specific languages that might be confused with Arabic
    if (lang !== 'ar' && lang !== 'de' && lang !== 'ru' && lang !== 'ja' && lang !== 'zh') {
      const containsArabic = transcriptItems.some(item =>
        item.text && /[\u0600-\u06FF]/.test(item.text)
      );

      if (containsArabic) {
        console.log('Detected Arabic transcript but requested language is not Arabic. Skipping this method.');
        return [];
      }
    }

    // Clean up the transcript items
    const cleanedItems = cleanupTranscriptItems(transcriptItems);
    console.log(`Cleaned ${cleanedItems.length} transcript items`);

    return cleanedItems;
  } catch (error) {
    console.error(`Error processing VTT content: ${error.message}`);
    // Return an empty array in case of error
    return [];
  }
}

/**
 * Decode HTML entities in text
 * @param {string} text - Text with HTML entities
 * @returns {string} Decoded text
 */
function decodeHtmlEntities(text) {
  if (!text) return '';

  // First use the 'he' library for standard HTML entities
  let decodedText = he.decode(text);

  // Handle common YouTube-specific entities and other escaped characters
  // that might not be properly decoded by the he library
  decodedText = decodedText
    // Common HTML entities
    .replace(/&#39;/g, "'")
    .replace(/&amp;#39;/g, "'")
    .replace(/&quot;/g, '"')
    .replace(/&amp;quot;/g, '"')
    .replace(/&amp;amp;/g, '&')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;nbsp;/g, ' ')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;lt;/g, '<')
    .replace(/&amp;gt;/g, '>')
    // Unicode characters
    .replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => String.fromCharCode(parseInt(hex, 16)))
    // Other common escaped characters
    .replace(/\\n/g, '\n')
    .replace(/\\t/g, '\t')
    .replace(/\\r/g, '\r')
    .replace(/\\\\/g, '\\')
    .replace(/\\'/g, "'")
    .replace(/\\"/g, '"');

  return decodedText;
}

/**
 * Clean up transcript items by fixing punctuation, removing duplicates, etc.
 * @param {Array} items - Transcript items to clean up
 * @returns {Array} Cleaned transcript items
 */
function cleanupTranscriptItems(items) {
  if (!items || items.length === 0) return [];

  const cleanedItems = [];

  // First pass: Clean up individual items
  const firstPassItems = [];
  for (let i = 0; i < items.length; i++) {
    if (!items[i].text || items[i].text.trim() === '') continue;

    const cleanedItem = { ...items[i] };

    // First, extract all timestamp tags for debugging
    const timestampTags = cleanedItem.text.match(/<[^>]*>/g) || [];
    if (timestampTags.length > 0) {
      console.log(`Found timestamp tags in text: ${JSON.stringify(timestampTags)}`);
    }

    // Remove all tags that look like timestamps (more comprehensive approach)
    cleanedItem.text = cleanedItem.text.replace(/<[^>]*\d+[:.]\d+[^>]*>/g, '');

    // Remove any HTML-like tags
    cleanedItem.text = cleanedItem.text.replace(/<\/?[a-z][^>]*>/gi, '');

    // Ensure spaces after punctuation
    cleanedItem.text = cleanedItem.text.replace(/([.!?,;:])([^\s])/g, '$1 $2');

    // Fix spacing around periods in numbers (e.g., "200 .000" -> "200.000")
    cleanedItem.text = cleanedItem.text.replace(/(\d+)\s+\.(\d+)/g, '$1.$2');

    // Fix incorrect spacing in numbers (e.g., "200. 000" -> "200.000")
    cleanedItem.text = cleanedItem.text.replace(/(\d+)\.(\s+)(\d+)/g, '$1.$3');

    // Normalize whitespace
    cleanedItem.text = cleanedItem.text.replace(/\s+/g, ' ').trim();

    // Skip if the text is empty after cleaning
    if (!cleanedItem.text) continue;

    firstPassItems.push(cleanedItem);
  }

  // Second pass: Remove duplicates and handle repetitive text
  for (let i = 0; i < firstPassItems.length; i++) {
    const currentItem = { ...firstPassItems[i] };

    // Skip if this is a duplicate of the previous item
    if (cleanedItems.length > 0) {
      const prevItem = cleanedItems[cleanedItems.length - 1];

      // Check for exact duplicates
      if (currentItem.text === prevItem.text) {
        console.log(`Skipping exact duplicate item: "${currentItem.text}"`);
        continue;
      }

      // Check for text that contains the previous text (common in auto-generated subtitles)
      if (currentItem.text.includes(prevItem.text)) {
        console.log(`Found text that contains previous text: "${currentItem.text}" contains "${prevItem.text}"`);
        // Only keep the new part
        const newPart = currentItem.text.replace(prevItem.text, '').trim();
        if (newPart) {
          console.log(`Keeping only new part: "${newPart}"`);
          currentItem.text = newPart;
        } else {
          console.log(`No new part found, skipping duplicate`);
          continue;
        }
      }

      // Check for text that is contained in the previous text
      if (prevItem.text.includes(currentItem.text)) {
        console.log(`Found text that is contained in previous text: "${currentItem.text}" is in "${prevItem.text}"`);
        console.log(`Skipping duplicate`);
        continue;
      }
    }

    // Special handling for repetitive text (common in English auto-generated subtitles)
    // This detects patterns like "text text text" and keeps only one instance
    const repetitivePattern = findRepetitivePattern(currentItem.text);
    if (repetitivePattern) {
      console.log(`Found repetitive pattern: "${repetitivePattern}" in "${currentItem.text}"`);
      currentItem.text = repetitivePattern;
    }

    cleanedItems.push(currentItem);
  }

  // Renumber the IDs
  for (let i = 0; i < cleanedItems.length; i++) {
    cleanedItems[i].id = i + 1;
  }

  // Ensure proper capitalization of sentences
  capitalizeFirstLetterOfSentences(cleanedItems);

  return cleanedItems;
}

/**
 * Find repetitive patterns in text
 * @param {string} text - Text to analyze
 * @returns {string|null} The non-repetitive pattern or null if none found
 */
function findRepetitivePattern(text) {
  if (!text || text.length < 10) return null;

  // Split the text into words
  const words = text.split(/\s+/);
  if (words.length < 6) return null; // Need at least 6 words to detect patterns

  // Try to find repeating sequences of words
  for (let patternLength = 3; patternLength <= Math.floor(words.length / 2); patternLength++) {
    // Get the first potential pattern
    const pattern = words.slice(0, patternLength).join(' ');

    // Check if this pattern repeats
    let isRepeating = true;
    let nonRepeatingText = '';

    for (let i = 0; i < words.length; i += patternLength) {
      const segment = words.slice(i, i + patternLength).join(' ');

      // If we've reached the end and have a partial segment
      if (i + patternLength > words.length) {
        nonRepeatingText = words.slice(i).join(' ');
        break;
      }

      // If this segment doesn't match the pattern
      if (segment !== pattern) {
        isRepeating = false;
        break;
      }
    }

    if (isRepeating) {
      // If we found a repeating pattern, return the pattern plus any non-repeating text
      return pattern + (nonRepeatingText ? ' ' + nonRepeatingText : '');
    }
  }

  // Check for repetition with slight variations (common in auto-generated subtitles)
  // This is a more complex approach that looks for similar phrases
  const segments = [];
  const segmentLength = 5; // Look for segments of 5 words

  for (let i = 0; i < words.length; i += segmentLength) {
    if (i + segmentLength <= words.length) {
      segments.push(words.slice(i, i + segmentLength).join(' '));
    } else {
      // Add the remaining words as the last segment
      segments.push(words.slice(i).join(' '));
    }
  }

  // If we have at least 3 segments, check for similarity
  if (segments.length >= 3) {
    // Compare the first segment with others to find similar ones
    const firstSegment = segments[0];
    const similarSegments = [firstSegment];

    for (let i = 1; i < segments.length; i++) {
      const segment = segments[i];

      // Simple similarity check: more than 50% of words are the same
      const firstWords = firstSegment.split(/\s+/);
      const segmentWords = segment.split(/\s+/);

      let commonWords = 0;
      for (const word of firstWords) {
        if (segmentWords.includes(word)) {
          commonWords++;
        }
      }

      const similarity = commonWords / Math.max(firstWords.length, segmentWords.length);

      if (similarity > 0.5) {
        // This segment is similar to the first one, so we'll consider it a repetition
        console.log(`Found similar segment: "${segment}" (similarity: ${similarity})`);
      } else {
        // This segment is different, so we'll keep it
        similarSegments.push(segment);
      }
    }

    // If we found repetitions, return the unique segments
    if (similarSegments.length < segments.length) {
      return similarSegments.join(' ');
    }
  }

  return null;
}

/**
 * Capitalize the first letter of each sentence across all transcript items
 * @param {Array} items - Transcript items to process
 */
function capitalizeFirstLetterOfSentences(items) {
  if (!items || items.length === 0) return;

  let shouldCapitalize = true;

  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    if (!item.text) continue;

    // Split the text into sentences
    const sentences = item.text.split(/([.!?]\s+)/).filter(Boolean);
    let result = '';

    for (let j = 0; j < sentences.length; j++) {
      const part = sentences[j];

      // If this is just a punctuation mark with space
      if (/^[.!?]\s+$/.test(part)) {
        result += part;
        shouldCapitalize = true;
        continue;
      }

      // If we should capitalize this part
      if (shouldCapitalize) {
        // Find the first letter in the sentence
        const firstLetterMatch = part.match(/[a-zA-Z]/);
        if (firstLetterMatch) {
          const firstLetterIndex = firstLetterMatch.index;
          result += part.substring(0, firstLetterIndex) +
                   part.charAt(firstLetterIndex).toUpperCase() +
                   part.substring(firstLetterIndex + 1);
        } else {
          result += part;
        }
        shouldCapitalize = false;
      } else {
        result += part;
      }
    }

    // Update the item text
    items[i].text = result;

    // Check if this item ends with a sentence-ending punctuation
    if (/[.!?]$/.test(item.text)) {
      shouldCapitalize = true;
    }
  }
}

/**
 * Format time in MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time
 */
function formatTime(seconds) {
  if (typeof seconds !== 'number' || isNaN(seconds)) {
    seconds = 0;
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  } else {
    return [
      minutes.toString().padStart(2, '0'),
      secs.toString().padStart(2, '0')
    ].join(':');
  }
}

module.exports = {
  processVttContent,
  cleanupTranscriptItems,
  formatTime
};
